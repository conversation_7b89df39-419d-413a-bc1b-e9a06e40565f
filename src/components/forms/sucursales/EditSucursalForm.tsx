import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Save, X, RotateCcw, Building2, Mail, Phone, MapPin, Trash2 } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { useForm } from "@tanstack/react-form";
import { UpdateSucursalData, EstadoSucursalLabels, Sucursal } from "@/types/sucursales";
import { $Enums } from "@/generated/prisma";

interface EditSucursalFormProps {
    sucursal: Sucursal;
    isSubmitting: boolean;
    onSubmit: (data: UpdateSucursalData) => void;
    onCancel: () => void;
    onDelete?: () => void;
    isDeleting?: boolean;
}

const getEstadoColor = (estado: $Enums.Estado) => {
    switch (estado) {
        case $Enums.Estado.ACTIVO:
            return "bg-green-100 text-green-800 border-green-200";
        case $Enums.Estado.INACTIVO:
            return "bg-red-100 text-red-800 border-red-200";
        case $Enums.Estado.PROSPECTO:
            return "bg-yellow-100 text-yellow-800 border-yellow-200";
        default:
            return "bg-gray-100 text-gray-800 border-gray-200";
    }
};

export function EditSucursalForm({
    sucursal,
    isSubmitting,
    onSubmit,
    onCancel,
    onDelete,
    isDeleting = false
}: EditSucursalFormProps) {
    const form = useForm({
        defaultValues: {
            nombre: sucursal.nombre,
            direccion: sucursal.direccion || "",
            telefono: sucursal.telefono || "",
            correo: sucursal.correo || "",
            estado: sucursal.estado,
        },
        onSubmit: async ({ value }) => {
            const submitData: UpdateSucursalData = {
                id: sucursal.id,
                nombre: value.nombre,
                direccion: value.direccion || undefined,
                telefono: value.telefono || undefined,
                correo: value.correo || undefined,
                estado: value.estado,
            };
            onSubmit(submitData);
        },
    });

    const handleReset = () => {
        form.setFieldValue("nombre", sucursal.nombre);
        form.setFieldValue("direccion", sucursal.direccion || "");
        form.setFieldValue("telefono", sucursal.telefono || "");
        form.setFieldValue("correo", sucursal.correo || "");
        form.setFieldValue("estado", sucursal.estado);
    };

    return (
        <div className="space-y-6">
            {/* Información de la sucursal existente */}

            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    form.handleSubmit();
                }}
                className="space-y-6"
            >
                {/* Información básica */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                        <Building2 className="h-5 w-5 text-blue-600" />
                        Editar Información
                    </h3>

                    {/* Nombre */}
                    <form.Field
                        name="nombre"
                        validators={{
                            onChange: ({ value }) => {
                                if (!value) return "El nombre es requerido";
                                if (value.length < 2) return "El nombre debe tener al menos 2 caracteres";
                                if (value.length > 100) return "El nombre no puede exceder 100 caracteres";
                                return undefined;
                            },
                        }}
                    >
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                    Nombre de la Sucursal *
                                </Label>
                                <Input
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onBlur={field.handleBlur}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    placeholder="Ej: Sucursal Centro"
                                    className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                                />
                                {field.state.meta.errors.length > 0 && (
                                    <p className="text-sm text-red-600">{field.state.meta.errors[0]}</p>
                                )}
                            </div>
                        )}
                    </form.Field>

                    {/* Dirección */}
                    <form.Field name="direccion">
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    Dirección
                                </Label>
                                <Textarea
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onBlur={field.handleBlur}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    placeholder="Dirección completa de la sucursal"
                                    rows={2}
                                    className="resize-none"
                                />
                            </div>
                        )}
                    </form.Field>

                    {/* Teléfono */}
                    <form.Field
                        name="telefono"
                        validators={{
                            onChange: ({ value }) => {
                                if (value && value.length > 20) return "El teléfono no puede exceder 20 caracteres";
                                return undefined;
                            },
                        }}
                    >
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                    <Phone className="h-4 w-4" />
                                    Teléfono
                                </Label>
                                <Input
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onBlur={field.handleBlur}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    placeholder="Ej: +52 55 1234 5678"
                                    className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                                />
                                {field.state.meta.errors.length > 0 && (
                                    <p className="text-sm text-red-600">{field.state.meta.errors[0]}</p>
                                )}
                            </div>
                        )}
                    </form.Field>

                    {/* Correo */}
                    <form.Field
                        name="correo"
                        validators={{
                            onChange: ({ value }) => {
                                if (value && value.length > 100) return "El correo no puede exceder 100 caracteres";
                                if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                                    return "Formato de correo inválido";
                                }
                                return undefined;
                            },
                        }}
                    >
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                    <Mail className="h-4 w-4" />
                                    Correo Electrónico
                                </Label>
                                <Input
                                    id={field.name}
                                    name={field.name}
                                    type="email"
                                    value={field.state.value}
                                    onBlur={field.handleBlur}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    placeholder="Ej: <EMAIL>"
                                    className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                                />
                                {field.state.meta.errors.length > 0 && (
                                    <p className="text-sm text-red-600">{field.state.meta.errors[0]}</p>
                                )}
                            </div>
                        )}
                    </form.Field>

                    {/* Estado */}
                    <form.Field name="estado">
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                    Estado
                                </Label>
                                <div className="flex items-center gap-3">
                                    <form.Subscribe
                                        selector={(state) => [state.values.estado]}
                                        children={([estado]) => (
                                            <Badge
                                                variant="outline"
                                                className={`${getEstadoColor(estado)} font-medium`}
                                            >
                                                {EstadoSucursalLabels[estado as $Enums.EstadoSucursal]}
                                            </Badge>
                                        )}
                                    />
                                    <Select
                                        value={field.state.value}
                                        onValueChange={(value) => field.handleChange(value as $Enums.EstadoSucursal)}
                                    >
                                        <SelectTrigger className="w-[200px]">
                                            <SelectValue placeholder="Seleccionar estado" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(EstadoSucursalLabels).map(([value, label]) => (
                                                <SelectItem key={value} value={value}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        )}
                    </form.Field>
                </div>

                {/* Botones de acción */}
                <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleReset}
                        className="flex items-center gap-2"
                        disabled={isSubmitting}
                    >
                        <RotateCcw className="h-4 w-4" />
                        Restaurar
                    </Button>

                    <div className="flex items-center gap-3">
                        {onDelete && (
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onDelete}
                                disabled={isSubmitting || isDeleting}
                                className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                            >
                                <Trash2 className="h-4 w-4" />
                                {isDeleting ? "Eliminando..." : "Eliminar"}
                            </Button>
                        )}

                        <Button
                            type="button"
                            variant="outline"
                            onClick={onCancel}
                            disabled={isSubmitting || isDeleting}
                            className="flex items-center gap-2"
                        >
                            <X className="h-4 w-4" />
                            Cancelar
                        </Button>

                        <form.Subscribe
                            selector={(state) => [state.canSubmit, state.isSubmitting]}
                            children={([canSubmit, isSubmittingForm]) => (
                                <Button
                                    type="submit"
                                    disabled={!canSubmit || isSubmitting || isSubmittingForm || isDeleting}
                                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                                >
                                    <Save className="h-4 w-4" />
                                    {isSubmitting ? "Guardando..." : "Guardar Cambios"}
                                </Button>
                            )}
                        />
                    </div>
                </div>
            </form>
        </div>
    );
}
