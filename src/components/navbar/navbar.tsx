'use client';

import { useState, useEffect } from "react";
import { NavLink, useLocation } from "react-router";
import { siteConfig } from "@/config/site.config";
import { Button } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>rigger, Sheet<PERSON>eader, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { CartIcon } from "@/components/cart/cart-icon";
import { CartDropdown } from "@/components/cart/cart-dropdown";
import { AnimatePresence } from "framer-motion"
import UserDropdown from "@/components/auth/user-dropdown"

import {
    Users,
    Package,
    LayoutDashboard,
    ShoppingBasket,
} from "lucide-react";

import {
    SignedIn,
    useUser,
    useAuth,
    RedirectToSignIn,
    Protect
} from "@clerk/nextjs";

export function Layout({ children }: { children: React.ReactNode }) {
    const { isLoaded: isUserLoaded, isSignedIn, user } = useUser();
    const { isLoaded: isAuthLoaded, has } = useAuth();
    const location = useLocation();

    const [isCartOpen, setIsCartOpen] = useState(false);
    const [isSheetOpen, setIsSheetOpen] = useState(false);

    // Cerrar el sheet cuando cambie la ubicación (página)
    useEffect(() => {
        setIsSheetOpen(false);
    }, [location.pathname]);

    if (!isSignedIn) {
        return <RedirectToSignIn />;
    }
    if (!isUserLoaded || !isAuthLoaded) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
            </div>
        );
    }



    const desktopNavLinks = (
        <>
            <Protect condition={(has) =>
                has({ role: "org:admin_bw" }) ||
                has({ role: "org:admin_suc_bw" }) ||
                has({ role: "org:vendedor_bw" })}>
                <NavLink
                    to="/docs"
                    className={({ isActive }) =>
                        `hidden lg:inline-flex items-center text-sm transition-colors ${isActive
                            ? "text-primary font-medium"
                            : "text-muted-foreground hover:text-foreground"
                        }`
                    }
                >
                    <Users className="inline-block h-4 w-4 mr-2" />
                    <span>Clientes</span>
                </NavLink>
            </Protect>

            <Protect
                condition={(has) =>
                    has({ role: "org:user_bw" }) ||
                    has({ role: "org:admin_bw" }) ||
                    has({ role: "org:vendedor_bw" }) ||
                    has({ role: "org:admin_suc_bw" })
                }
            >
                <NavLink
                    to="/productos"
                    className={({ isActive }) =>
                        `hidden lg:inline-flex items-center text-sm transition-colors ${isActive
                            ? "text-primary font-medium"
                            : "text-muted-foreground hover:text-foreground"
                        }`
                    }
                >
                    <Package className="inline-block h-4 w-4 mr-2" />
                    <span>Productos</span>
                </NavLink>
            </Protect>

            <Protect
                condition={(has) =>
                    has({ role: "org:admin_bw" }) ||
                    has({ role: "org:vendedor_bw" }) ||
                    has({ role: "org:admin_suc_bw" })
                }
            >
                <NavLink
                    to="/ventas"
                    className={({ isActive }) =>
                        `hidden lg:inline-flex items-center text-sm transition-colors ${isActive
                            ? "text-primary font-medium"
                            : "text-muted-foreground hover:text-foreground"
                        }`
                    }
                >
                    <ShoppingBasket className="inline-block h-4 w-4 mr-2" />
                    <span>Ventas</span>
                </NavLink>
            </Protect>

            <Protect
                condition={(has) =>
                    has({ role: "org:admin_bw" })
                }
            >
                <NavLink
                    to="/admin"
                    className={({ isActive }) =>
                        `hidden lg:inline-flex items-center text-sm transition-colors ${isActive
                            ? "text-primary font-medium"
                            : "text-muted-foreground hover:text-foreground"
                        }`
                    }
                >
                    <LayoutDashboard className="inline-block h-4 w-4 mr-2" />
                    <span>Admin</span>
                </NavLink>
            </Protect>

        </>
    );

    const mobileNavLinks = (
        <nav className="flex flex-col space-y-2 p-4">
            <>
                <Protect condition={(has) =>
                    has({ role: "org:admin_bw" }) ||
                    has({ role: "org:admin_suc_bw" }) ||
                    has({ role: "org:vendedor_bw" })
                }>
                    <NavLink
                        to="/docs"
                        className={({ isActive }) =>
                            `block p-3 rounded-md transition-colors ${isActive
                                ? "bg-primary text-primary-foreground font-medium"
                                : "text-muted-foreground hover:bg-muted hover:text-foreground"
                            }`
                        }
                    >
                        <div className="flex items-center">
                            <Users className="inline-block h-5 w-5 mr-3" />
                            <span>Clientes</span>
                        </div>
                    </NavLink>
                </Protect>

                <Protect
                    condition={(has) =>
                        has({ role: "org:admin_bw" }) ||
                        has({ role: "org:vendedor_bw" }) ||
                        has({ role: "org:admin_suc_bw" })
                    }
                >
                    <NavLink
                        to="/ventas"
                        className={({ isActive }) =>
                            `block p-3 rounded-md transition-colors ${isActive
                                ? "bg-primary text-primary-foreground font-medium"
                                : "text-muted-foreground hover:bg-muted hover:text-foreground"
                            }`
                        }
                    >
                        <div className="flex items-center">
                            <ShoppingBasket className="inline-block h-5 w-5 mr-3" />
                            <span>Ventas</span>
                        </div>
                    </NavLink>
                </Protect>
                <Protect
                    condition={(has) =>
                        has({ role: "org:admin_bw" })
                    }
                >
                    <NavLink
                        to="/admin"
                        className={({ isActive }) =>
                            `block p-3 rounded-md transition-colors ${isActive
                                ? "bg-primary text-primary-foreground font-medium"
                                : "text-muted-foreground hover:bg-muted hover:text-foreground"
                            }`
                        }
                    >
                        <div className="flex items-center">
                            <LayoutDashboard className="inline-block h-5 w-5 mr-3" />
                            <span>Admin</span>
                        </div>
                    </NavLink>
                </Protect>

                <Protect
                    condition={(has) =>
                        has({ role: "org:user_bw" }) ||
                        has({ role: "org:admin_bw" }) ||
                        has({ role: "org:vendedor_bw" }) ||
                        has({ role: "org:admin_suc_bw" })
                    }
                >
                    <NavLink
                        to="/productos"
                        className={({ isActive }) =>
                            `block p-3 rounded-md transition-colors ${isActive
                                ? "bg-primary text-primary-foreground font-medium"
                                : "text-muted-foreground hover:bg-muted hover:text-foreground"
                            }`
                        }
                    >
                        <div className="flex items-center">
                            <Package className="inline-block h-5 w-5 mr-3" />
                            <span>Productos</span>
                        </div>
                    </NavLink>
                </Protect>
            </>
        </nav>
    );

    return (
        <>
            {/* Navigation */}
            <nav className="sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center space-x-8">
                            <NavLink
                                to="/"
                                className="text-xl font-bold text-primary hover:text-primary/80 transition-colors"
                            >
                                {siteConfig.name}
                            </NavLink>
                            <div className="hidden lg:flex space-x-6">
                                {desktopNavLinks}
                            </div>
                        </div>

                        <div className="flex items-center gap-2">


                            <div className="flex items-center gap-2">
                                <CartIcon onClick={() => setIsCartOpen(true)} />
                                <SignedIn>
                                    <UserDropdown />
                                </SignedIn>
                            </div>


                            <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                                <SheetTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="lg:hidden"
                                    >
                                        <Menu className="h-6 w-6" />
                                    </Button>
                                </SheetTrigger>
                                <SheetContent side="right">
                                    <SheetHeader>
                                        <SheetTitle>Menú de navegación</SheetTitle>
                                    </SheetHeader>
                                    {mobileNavLinks}
                                </SheetContent>
                            </Sheet>
                        </div>
                    </div>
                </div>
            </nav>
            <AnimatePresence>
                {isCartOpen && <CartDropdown isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />}
            </AnimatePresence>

            {/* Main Content */}
            <main className="flex-1">
                {children}
            </main>
        </>
    );
}