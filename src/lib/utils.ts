import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { toZonedTime } from "date-fns-tz";
import { $Enums } from "@/generated/prisma";
import { createHash } from "crypto";
import { nanoid } from "nanoid";
import { CurrencyType } from "@/types/utils";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function NowCdMxString() {
  return toZonedTime(new Date(), "America/Mexico_City").toISOString();
}

export function createPathFile(
  id: string,
  filename: string,
  type: $Enums.TipoReferencia
) {
  const now = NowCdMxString();
  const hash = createHash("sha256").update(`${filename}_${now}`).digest("hex");

  if (process.env.NODE_ENV === "production") {
    return `${type}/${id}/${hash.slice(0, 16)}`;
  }

  return `DEV/${type}/${id}/${hash.slice(0, 16)}`;
}

export function createVentaFolio(autoId: string, clienteId: string): string {
  return `VNT-${autoId.slice(0, 4)}-${clienteId.slice(0, 4)}`;
}

export function createProductSKU() {
  return `PRD-${nanoid(8).toUpperCase()}`;
}

export const calculateTotals = (total: number, conIva: boolean) => {
  if (!conIva) {
    return { subTotal: total, iva: 0, total };
  }

  // Ya viene con iva por lo que en realidad solo calculamos el iva
  const iva = Math.round(total * (1 - 1 / 1.16));
  const subTotal = Math.round(total - iva);
  return { subTotal, iva, total };
};

export function formatCurrencyView(
  Currency: CurrencyType,
  usdOption: number,
  mxnOption: number,
  currencyName: boolean = false
) {
  return `${Currency === CurrencyType.USD ? usdOption.toLocaleString("en-US") : mxnOption.toLocaleString("es-MX")} ${currencyName ? Currency : ""}`;
}
