import { protectedProcedure, router } from "@/server/trpc";
import { clientCarSchema } from "@/types/clientes";
import { autoSchema } from "@/types/autos";

import { z } from "zod";

import { NowCdMxString } from "@/lib/utils";

export const clientRouter = router({
  getClients: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.clientes.findMany();
  }),

  getClientsByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      return await ctx.prisma.clientes.findMany({
        where: {
          OR: [
            { nombre: { contains: input, mode: "insensitive" } },
            { apellidoPaterno: { contains: input, mode: "insensitive" } },
            { apellidoMaterno: { contains: input, mode: "insensitive" } },
            { telefono: { contains: input, mode: "insensitive" } },
            { correo: { contains: input, mode: "insensitive" } },
          ],
        },
      });
    }),

  getClientsWithCarsByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      return await ctx.prisma.clientes.findMany({
        where: {
          OR: [
            { nombre: { contains: input, mode: "insensitive" } },
            { apellidoPaterno: { contains: input, mode: "insensitive" } },
            { apellidoMaterno: { contains: input, mode: "insensitive" } },
            { telefono: { contains: input, mode: "insensitive" } },
            { correo: { contains: input, mode: "insensitive" } },
          ],
        },
        include: {
          Autos: true,
        },
      });
    }),

  createClientCar: protectedProcedure
    .input(autoSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const now = NowCdMxString();

        return await tx.autos.create({
          data: {
            placas: input.plates,
            a_o: input.year,
            tipo: input.type,
            modelo: input.model,
            id_cliente: input.idClient,
            creadoPor: "admin",
            actualizadoPor: "admin",
          },
        });
      });

      return { success: true, id: result.id };
    }),

  createClientWithCar: protectedProcedure
    .input(clientCarSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const now = NowCdMxString();

        return await tx.clientes.create({
          data: {
            nombre: input.cliente.name,
            apellidoPaterno: input.cliente.subnameP,
            apellidoMaterno: input.cliente.subnameM,
            telefono: input.cliente.phone,
            correo: input.cliente.email,
            estado: input.cliente.isActive,
            creadoPor: "admin",
            actualizadoPor: "admin",
            Autos: {
              create: {
                placas: input.auto.plates,
                a_o: input.auto.year,
                tipo: input.auto.type,
                modelo: input.auto.model,
                creadoPor: "admin",
                actualizadoPor: "admin",
              },
            },
          },
        });
      });

      return { success: true, id: result.id };
    }),
});

export type ClientRouter = typeof clientRouter;
