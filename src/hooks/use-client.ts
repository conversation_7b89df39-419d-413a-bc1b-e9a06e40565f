import { useTRPC } from "@/_trpc/client";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { $Enums } from "@/generated/prisma";

interface UseClientParam {
  query?: string;
  isActive?: $Enums.Estado;
  pageIndex?: number;
  pageSize?: number;
}

export const useClientsByQuery = (query: string) => {
  const trpc = useTRPC();

  const getClientsByQuery = useQuery(
    trpc.client.getClientsByQuery.queryOptions(query ?? "", {
      enabled: query?.length! > 2,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getClientsByQuery,
  };
};

export const useClientsWithCarsByQuery = (query: string) => {
  const trpc = useTRPC();

  const getClientsWithCarsByQuery = useQuery(
    trpc.client.getClientsWithCarsByQuery.queryOptions(query ?? "", {
      enabled: query?.length! > 2,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getClientsWithCarsByQuery,
  };
};

export const useClientMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const createClientMutation = useMutation(
    trpc.client.createClientWithCar.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsWithCarsByQuery.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClients.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsByQuery.queryKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear el cliente:", error);
      },
      onSettled: () => {
        console.log("Creación de cliente finalizada");
      },
    })
  );

  return {
    ...createClientMutation,
  };
};

export const useCarMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.client.createClientCar.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsWithCarsByQuery.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClients.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsByQuery.queryKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear el auto:", error);
      },
      onSettled: () => {
        console.log("Creación de auto finalizada");
      },
    })
  );
};
